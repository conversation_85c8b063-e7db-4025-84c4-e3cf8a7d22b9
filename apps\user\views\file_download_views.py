#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : file_download_views.py
<AUTHOR> JT_DA
@Date     : 2025/07/31
@File_Desc: 通用文件下载视图，支持AssetPackageManagementFile和MediationCaseFile模型的安全文件下载
"""

import os
import logging
import mimetypes
from django.http import FileResponse
from django.core.exceptions import PermissionDenied
from rest_framework.generics import GenericAPIView
from rest_framework import serializers

from apps.data_governance.models import AssetPackageManagementFile
from apps.mediation_management.models import MediationCaseFile
from utils.permission_helper import MyPermission, WechatFaceAuthPermission
from utils.ajax_result import AjaxResult

# 获取日志记录器
logger = logging.getLogger(__name__)


class FileDownloadSerializer(serializers.Serializer):
    """
    文件下载接口的参数验证序列化器，用于规范化URL路径参数的格式和约束。
    该序列化器主要用于API文档自动生成和开发调试时的参数验证。
    """

    secure_token = serializers.UUIDField(
        help_text="文件安全标识符，UUID格式，用于安全地标识和下载文件，防止路径暴露"
    )


class FileDownloadView(GenericAPIView):
    """
    安全文件下载视图，提供资产包附件和调解案件附件的安全下载功能。
    该视图使用UUID安全标识符替代直接文件路径，防止路径暴露和枚举攻击，
    实现了完整的权限验证和安全防护机制。
    """

    # 配置序列化器类用于API文档生成
    serializer_class = FileDownloadSerializer

    # 配置权限类：使用Django REST framework的位运算符实现OR逻辑
    # 满足MyPermission或WechatFaceAuthPermission任一权限即可访问
    authentication_classes = []
    pagination_class = []
    # permission_classes = [MyPermission | WechatFaceAuthPermission]

    # 支持的文件模型列表，用于安全验证
    SUPPORTED_MODELS = [AssetPackageManagementFile, MediationCaseFile]

    def get(self, request, secure_token):
        """
        通过安全标识符下载指定的附件文件，支持资产包附件和调解案件附件的安全下载。
        该接口使用UUID安全标识符替代直接文件路径，防止路径暴露、信息泄露和枚举攻击，
        实现了完整的权限验证和安全防护机制。

        请求参数：
        - secure_token (UUID, 必需): 文件安全标识符，UUID格式，用于安全地标识文件

        请求数据示例：
        GET /user/files/download/550e8400-e29b-41d4-a716-************/

        响应数据结构：
        成功响应：返回文件流，包含以下HTTP响应头
        - Content-Type: 根据文件类型自动设置（如 application/pdf、application/vnd.openxmlformats-officedocument.spreadsheetml.sheet）
        - Content-Disposition: attachment; filename="原始文件名.扩展名"
        - Content-Length: 文件大小（字节）

        错误响应：
        {
            "code": 404,
            "msg": "文件记录不存在或已被删除",
            "data": null
        }
        {
            "code": 403,
            "msg": "文件路径不安全",
            "data": null
        }
        {
            "code": 400,
            "msg": "无效的安全标识符格式",
            "data": null
        }
        """
        try:
            # 验证安全标识符格式
            try:
                # 尝试解析UUID格式，确保输入的安全性
                import uuid as uuid_module
                uuid_obj = uuid_module.UUID(str(secure_token))
            except (ValueError, TypeError) as e:
                logger.warning(f"无效的安全标识符格式: {secure_token}, 错误: {str(e)}")
                return AjaxResult.fail(msg="无效的安全标识符格式")

            # 查询文件记录（通过secure_token查找，支持多个模型）
            file_instance = None
            model_name = None

            # 遍历支持的模型，查找匹配的文件记录
            for model_class in self.SUPPORTED_MODELS:
                try:
                    file_instance = model_class.objects.get(secure_token=secure_token)
                    model_name = model_class.__name__  # 获取模型名称用于日志记录
                    break
                except model_class.DoesNotExist:
                    continue

            # 如果在所有模型中都未找到文件记录
            if file_instance is None:
                logger.warning(f"文件记录不存在: secure_token={secure_token}")
                return AjaxResult.not_found(msg="文件记录不存在或已被删除")

            # 检查文件字段是否存在
            if not file_instance.file:
                logger.warning(f"文件字段为空: {model_name} secure_token={secure_token}")
                return AjaxResult.not_found(msg="文件不存在")

            # 获取文件的物理路径
            file_path = file_instance.file.path

            # 验证文件是否在磁盘上存在
            if not os.path.exists(file_path):
                logger.error(f"物理文件不存在: {file_path}")
                return AjaxResult.not_found(msg="文件不存在")

            # 安全检查：防止路径遍历攻击
            # 确保文件路径在允许的上传目录内
            if not self._is_safe_path(file_path):
                logger.error(f"检测到路径遍历攻击尝试: {file_path}")
                raise PermissionDenied("文件路径不安全")

            # 获取文件的原始名称，优先使用file_name字段
            original_filename = getattr(file_instance, "file_name", None) or os.path.basename(file_path)

            # 记录文件下载日志（使用安全标识符而非文件路径）
            # logger.info(
            #     f"用户 {request.user.username} 下载文件: {model_name} secure_token={secure_token}, 原始文件名={original_filename}"
            # )

            # 创建文件响应
            response = FileResponse(open(file_path, "rb"), as_attachment=True, filename=original_filename)

            # 设置Content-Type响应头
            content_type, _ = mimetypes.guess_type(file_path)
            if content_type:
                response["Content-Type"] = content_type

            # 设置Content-Length响应头
            response["Content-Length"] = os.path.getsize(file_path)

            return response

        except PermissionDenied as e:
            # 权限错误
            logger.error(f"权限错误: {str(e)}")
            return AjaxResult.forbidden(msg=str(e))
        except Exception as e:
            # 其他未预期的错误
            logger.error(f"文件下载失败: {str(e)}")
            return AjaxResult.server_error(msg=f"文件下载失败: {str(e)}")

    def _is_safe_path(self, file_path):
        """
        验证文件路径的安全性，防止路径遍历攻击和非法文件访问。
        该方法通过路径规范化和前缀检查，确保只能访问系统允许的上传目录内的文件。

        请求参数：
        - file_path (字符串, 必需): 待验证的文件绝对路径

        请求数据示例：
        该方法为内部方法，通过代码调用：
        is_safe = self._is_safe_path("/path/to/upload/file.pdf")

        响应数据结构：
        返回布尔值：
        - True: 文件路径安全，位于允许的上传目录内
        - False: 文件路径不安全，可能存在路径遍历攻击或路径验证异常
        """
        try:
            from django.conf import settings

            # 获取允许的上传目录路径
            upload_dir = os.path.abspath(settings.UPLOAD_DIR)

            # 规范化文件路径
            normalized_file_path = os.path.abspath(file_path)

            # 检查文件路径是否在上传目录内
            return normalized_file_path.startswith(upload_dir)

        except Exception as e:
            # 如果路径验证过程中出现异常，为安全起见返回False
            logger.error(f"路径安全性验证失败: {str(e)}")
            return False
