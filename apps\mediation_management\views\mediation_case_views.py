#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : mediation_case_views.py
<AUTHOR> JT_DA
@Date     : 2025/07/22
@File_Desc: 调解案件相关视图
"""

import logging
import os
from datetime import datetime
from django.db.models import Q
from django.core.files.base import ContentFile
from rest_framework.generics import GenericAPIView
from rest_framework import serializers

# ReportLab PDF生成相关导入
from reportlab.lib.pagesizes import A4
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib.enums import TA_CENTER
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Image, Table, TableStyle
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont

from apps.mediation_management.models import MediationCase, MediationPlan, MediationCaseFile
from apps.counterparty.models import DebtorBasicInfo
from apps.mediation_management.serializers.mediation_case_query_serializers import (
    MediationCaseByDebtorSerializer,
    MediationContentSerializer,
    MediationPlanConfigSerializer,
    MediationCaseQuerySerializer,
    MediationCaseWechatSerializer,
)
from apps.user.models import SystemUser
from utils.permission_helper import WechatFaceAuthPermission
from utils.ajax_result import AjaxResult
from utils.expression_calculator import calculate_expression_with_asset_data
from utils.file_security_helper import FileSecurityHelper

# 获取日志记录器
logger = logging.getLogger(__name__)


class MediationCaseByDebtorView(GenericAPIView):
    """
    调解案件按债务人查询视图

    提供基于债务人身份信息的调解案件数量统计查询功能，用于快速了解特定债务人的案件情况。
    该视图通过债务人姓名和身份证号精确匹配债务人记录，统计其关联的所有调解案件数量。
    无需身份认证，支持外部系统快速查询案件统计信息。
    """

    # 无需身份认证
    authentication_classes = []
    # 无需权限认证
    permission_classes = []

    serializer_class = MediationCaseByDebtorSerializer

    def get(self, request, *args, **kwargs):
        """
        根据债务人姓名和身份证号查询相关调解案件数量

        通过债务人的姓名和身份证号精确匹配债务人记录，统计该债务人关联的所有调解案件数量。
        该接口无需身份认证，支持外部系统快速查询案件统计信息。

        **请求参数：**

        **查询参数：**
        - name (字符串, 必需): 债务人姓名，用于精确匹配债务人记录
        - id_card (字符串, 必需): 债务人身份证号，用于精确匹配债务人记录

        **请求数据示例：**
        ```
        GET /mediation_management/mediation_case/by_debtor/?name=张三&id_card=110101199001011234
        ```

        **响应数据结构：**
        成功响应：
        ```json
        {
            "code": 200,
            "msg": "查询成功",
            "state": "success",
            "data": "已查询到【3】个相关调解案件"
        }
        ```

        错误响应：
        ```json
        {
            "code": 404,
            "msg": "未找到对应的债务人信息",
            "state": "fail"
        }
        ```
        """
        # 获取查询参数
        name = request.query_params.get("name")
        id_card = request.query_params.get("id_card")

        # 验证参数是否提供
        if not name:
            return AjaxResult.fail(msg="请提供债务人姓名参数")
        if not id_card:
            return AjaxResult.fail(msg="请提供债务人身份证号参数")

        try:
            # 根据姓名和身份证号查询债务人
            debtor = DebtorBasicInfo.objects.get(debtor_name=name, id_number=id_card)

            # 统计该债务人相关的调解案件数量
            case_count = MediationCase.objects.filter(debtor=debtor).count()

            # 构建响应数据
            result_message = f"已查询到【{case_count}】个相关调解案件"

            # 返回成功响应
            return AjaxResult.success(msg="查询成功", data=result_message)

        except DebtorBasicInfo.DoesNotExist:
            # 债务人不存在时返回404错误
            return AjaxResult(code=404, msg="未找到对应的债务人信息", state="fail").to_json_response()
        except Exception as e:
            # 其他异常处理
            return AjaxResult.fail(msg=f"查询失败：{str(e)}")


class MediationContentView(GenericAPIView):
    """
    调解信息内容获取视图

    提供调解案件配置信息获取和表达式计算功能，用于获取调解案件的详细配置内容。
    该视图通过调解案件ID查询对应的调解案件，获取关联资产包的mediation_config配置，
    并对配置中的表达式进行动态计算处理，同时收集案件和资产包的附件信息。
    """

    # 配置序列化器类
    serializer_class = MediationContentSerializer

    def get(self, request, mediation_case_id, *args, **kwargs):
        """
        获取调解信息内容

        根据调解案件ID获取案件的配置信息并进行表达式计算，返回包含计算结果的完整配置数据。
        该接口会获取关联资产包的mediation_config配置，对其中的表达式进行动态计算，
        同时收集调解案件和关联资产包的所有附件信息。

        **请求参数：**

        **路径参数：**
        - mediation_case_id (整数, 必需): 调解案件ID，通过URL路径参数传递

        **请求数据示例：**
        ```
        GET /mediation_management/mediation_case/123/content/
        ```

        **响应数据结构：**

        成功响应：
        ```json
        {
            "code": 200,
            "msg": "获取成功",
            "state": "success",
            "data": {
                "case_id": 123,
                "mediation_config": [
                    {
                        "id": "GZTJ6dz1z75km",
                        "title": "应还金额",
                        "logic_type": "result_calculation",
                        "expression": "{债权总额}-{已还金额}",
                        "value": "150000.00"
                    },
                    {
                        "id": "GZTJ6dz1z75kn",
                        "title": "债务人姓名",
                        "logic_type": "text_formatting",
                        "expression": "{债务人姓名}",
                        "value": "张三"
                    }
                ],
                "attachments": [
                    {
                        "id": 1,
                        "name": "调解协议.pdf",
                        "download_url": "/secure-download/abc123def456",
                        "type": "document",
                        "source": "case"
                    },
                    {
                        "id": 2,
                        "name": "资产包明细.xlsx",
                        "download_url": "/secure-download/xyz789uvw012",
                        "type": "spreadsheet",
                        "source": "asset_package"
                    }
                ]
            }
        }
        ```

        错误响应：
        ```json
        {
            "code": 404,
            "msg": "未找到对应的调解案件",
            "state": "fail"
        }
        ```
        """
        try:
            # 验证调解案件ID参数
            serializer_data = {"mediation_case_id": mediation_case_id}
            serializer = self.get_serializer(data=serializer_data)
            serializer.is_valid(raise_exception=True)

            # 记录请求日志
            logger.info(f"开始处理调解信息内容获取，调解案件ID: {mediation_case_id}")

            # 获取调解案件对象
            mediation_case = MediationCase.objects.get(id=mediation_case_id)

            # 检查调解案件是否关联资产包
            if not mediation_case.asset_package:
                logger.error(f"调解案件 {mediation_case_id} 没有关联资产包")
                return AjaxResult.fail(msg="调解案件没有关联资产包")

            # 获取调解配置信息 - 优先使用确认的配置快照，备用方案为资产包配置
            mediation_config = None
            if mediation_case.confirmed_mediation_config:
                # 优先使用：调解案件确认时的配置快照
                mediation_config = mediation_case.confirmed_mediation_config
                logger.info(f"使用调解案件 {mediation_case_id} 的确认配置快照")
            else:
                # 备用方案：使用关联资产包的实时配置
                mediation_config = mediation_case.asset_package.mediation_config
                logger.info(f"使用调解案件 {mediation_case_id} 关联资产包的实时配置")

            if not mediation_config:
                logger.error(f"调解案件 {mediation_case_id} 没有可用的调解配置信息")
                return AjaxResult.fail(msg="调解案件没有可用的调解配置信息")

            # 检查mediation_config是否为列表格式
            if not isinstance(mediation_config, list):
                logger.error(f"调解案件 {mediation_case_id} 的mediation_config不是列表格式")
                return AjaxResult.fail(msg="调解配置信息格式错误")

            # 处理mediation_config中的每个对象
            processed_config = []
            asset_package = mediation_case.asset_package

            # 获取调解案件在资产包中的行号，默认为1
            row_number = mediation_case.asset_package_row_number or 1

            for config_item in mediation_config:
                # 复制原始配置对象
                processed_item = config_item.copy()

                # 检查配置对象是否包含必要字段
                if not isinstance(config_item, dict):
                    logger.warning(f"跳过非字典格式的配置项: {config_item}")
                    processed_config.append(processed_item)
                    continue

                logic_type = config_item.get("logic_type")
                expression = config_item.get("expression")

                # 如果没有logic_type或expression，直接添加到结果中
                if not logic_type or not expression:
                    logger.warning(f"配置项缺少logic_type或expression字段: {config_item}")
                    processed_item["value"] = None
                    processed_config.append(processed_item)
                    continue

                # 使用工具函数计算表达式
                calculation_result = calculate_expression_with_asset_data(
                    asset_package=asset_package, row_number=row_number, expression=expression, logic_type=logic_type
                )

                # 将计算结果添加到配置对象中
                if calculation_result["success"]:
                    processed_item["value"] = calculation_result["result"]
                    logger.info(
                        f"表达式计算成功，配置ID: {config_item.get('id', 'unknown')}, 结果: {calculation_result['result']}"
                    )
                else:
                    processed_item["value"] = None
                    logger.error(
                        f"表达式计算失败，配置ID: {config_item.get('id', 'unknown')}, 错误: {calculation_result['error']}"
                    )

                processed_config.append(processed_item)

            # 收集附件数据
            attachments_data = []

            # 收集调解案件的附件
            case_attachments = mediation_case.attachments.all()
            for attachment in case_attachments:
                download_url = FileSecurityHelper.generate_secure_download_url(attachment)

                attachment_info = {
                    "id": attachment.id,
                    "name": attachment.file_name,
                    "download_url": download_url,
                    "type": self._get_file_type(attachment.file_name) if attachment.file_name else None,
                    "source": "case",
                }
                attachments_data.append(attachment_info)
                logger.info(f"添加调解案件附件: {attachment.file_name}, ID: {attachment.id}")

            # 收集关联资产包的附件
            if asset_package:
                package_attachments = asset_package.attachments.all()
                for attachment in package_attachments:
                    # 使用安全标识符构建文件下载URL
                    download_url = FileSecurityHelper.generate_secure_download_url(attachment)

                    attachment_info = {
                        "id": attachment.id,
                        "name": attachment.file_name,
                        "download_url": download_url,
                        "type": self._get_file_type(attachment.file_name) if attachment.file_name else None,
                        "source": "asset_package",
                    }
                    attachments_data.append(attachment_info)
                    logger.info(f"添加资产包附件: {attachment.file_name}, ID: {attachment.id}")

            # 构建响应数据结构
            response_data = {
                "case_id": mediation_case_id,
                "mediation_config": processed_config,
                "attachments": attachments_data,
            }

            logger.info(
                f"调解信息内容处理完成，调解案件ID: {mediation_case_id}, 处理了 {len(processed_config)} 个配置项, {len(attachments_data)} 个附件"
            )
            return AjaxResult.success(msg="获取成功", data=response_data)

        except serializers.ValidationError as e:
            # 处理序列化器验证错误
            error_messages = []
            if hasattr(e, "detail"):
                for _, messages in e.detail.items():
                    if isinstance(messages, list):
                        error_messages.extend([str(msg) for msg in messages])
                    else:
                        error_messages.append(str(messages))
            error_msg = "; ".join(error_messages) if error_messages else str(e)
            logger.error(f"参数验证失败: {error_msg}")
            return AjaxResult.fail(msg=f"参数验证失败: {error_msg}")

        except MediationCase.DoesNotExist:
            logger.error(f"调解案件 {mediation_case_id} 不存在")
            return AjaxResult(code=404, msg="未找到对应的调解案件", state="fail").to_json_response()

        except Exception as e:
            logger.error(f"处理调解信息内容获取请求时发生异常: {str(e)}")
            return AjaxResult.fail(msg="服务器内部错误")

    def _get_file_type(self, file_name):
        """
        根据文件名获取文件类型

        通过分析文件扩展名确定文件的类型分类，用于前端显示不同的文件图标和处理方式。
        支持常见的文档、表格、文本和图片文件类型识别。

        **请求参数：**
        - file_name (字符串, 必需): 文件名，包含文件扩展名

        **请求数据示例：**
        该方法为内部方法，通过代码调用：
        ```python
        file_type = self._get_file_type("调解协议.pdf")
        ```

        **响应数据结构：**
        返回文件类型字符串：
        - "document": PDF、Word文档
        - "spreadsheet": Excel表格文件
        - "text": 纯文本文件
        - "image": 图片文件
        - "unknown": 未知类型或无扩展名
        """
        if not file_name:
            return "unknown"

        # 获取文件扩展名
        file_extension = file_name.lower().split(".")[-1] if "." in file_name else ""

        # 定义文件类型映射
        type_mapping = {
            "pdf": "document",
            "doc": "document",
            "docx": "document",
            "xls": "spreadsheet",
            "xlsx": "spreadsheet",
            "txt": "text",
            "jpg": "image",
            "jpeg": "image",
            "png": "image",
            "gif": "image",
        }

        return type_mapping.get(file_extension, "unknown")


class MediationPlanConfigView(GenericAPIView):
    """
    调解方案配置获取视图

    提供调解案件相关的调解方案配置信息获取和表达式计算功能。
    该视图通过调解案件ID查询对应的调解案件，获取所有相关的已生效调解方案的plan_config配置，
    并对配置中的表达式进行动态计算处理，返回按调解方案分组的嵌套结构数据。
    """

    # 配置序列化器类
    serializer_class = MediationPlanConfigSerializer

    def get(self, request, mediation_case_id, *args, **kwargs):
        """
        获取调解方案配置信息

        根据调解案件ID获取所有相关的已生效调解方案配置信息并进行表达式计算。
        该接口会查询与指定调解案件相关的所有已生效调解方案，对方案配置中的表达式进行动态计算，
        返回按调解方案分组的嵌套结构数据。

        **请求参数：**

        **路径参数：**
        - mediation_case_id (整数, 必需): 调解案件ID，通过URL路径参数传递

        **请求数据示例：**
        ```
        GET /mediation_management/mediation_case/123/plan_config/
        ```

        **响应数据结构：**
        
        成功响应：
        ```json
        {
            "code": 200,
            "msg": "获取成功",
            "state": "success",
            "data": [
                {
                    "plan_id": 1,
                    "plan_name": "标准还款方案",
                    "plan_config": [
                        {
                            "id": "PLAN001",
                            "title": "还款金额",
                            "logic_type": "result_calculation",
                            "expression": "{债权总额}*0.8",
                            "value": "120000.00"
                        },
                        {
                            "id": "PLAN002",
                            "title": "还款期限",
                            "logic_type": "text_formatting",
                            "expression": "12个月",
                            "value": "12个月"
                        }
                    ]
                },
                {
                    "plan_id": 2,
                    "plan_name": "优惠还款方案",
                    "plan_config": [
                        {
                            "id": "PLAN003",
                            "title": "优惠金额",
                            "logic_type": "result_calculation",
                            "expression": "{债权总额}*0.6",
                            "value": "90000.00"
                        }
                    ]
                }
            ]
        }
        ```

        错误响应：
        ```json
        {
            "code": 404,
            "msg": "未找到对应的调解案件",
            "state": "fail"
        }
        ```
        """
        try:
            # 验证调解案件ID参数
            serializer_data = {"mediation_case_id": mediation_case_id}
            serializer = self.get_serializer(data=serializer_data)
            serializer.is_valid(raise_exception=True)

            # 记录请求日志
            logger.info(f"开始处理调解方案配置获取，调解案件ID: {mediation_case_id}")

            # 获取调解案件对象
            mediation_case = MediationCase.objects.get(id=mediation_case_id)

            # 优先级策略：优先使用确认的方案配置快照，备用方案为查询已生效调解方案
            if mediation_case.confirmed_plan_config:
                # 优先使用：调解案件确认时的方案配置快照
                logger.info(f"使用调解案件 {mediation_case_id} 的确认方案配置快照")
                return self._process_confirmed_plan_config(mediation_case, mediation_case_id)
            else:
                # 备用方案：查询相关的已生效调解方案
                logger.info(f"使用调解案件 {mediation_case_id} 的实时调解方案查询")
                return self._process_active_mediation_plans(mediation_case, mediation_case_id)

        except serializers.ValidationError as e:
            # 处理序列化器验证错误
            error_messages = []
            if hasattr(e, "detail"):
                for _, messages in e.detail.items():
                    if isinstance(messages, list):
                        error_messages.extend([str(msg) for msg in messages])
                    else:
                        error_messages.append(str(messages))
            error_msg = "; ".join(error_messages) if error_messages else str(e)
            logger.error(f"参数验证失败: {error_msg}")
            return AjaxResult.fail(msg=f"参数验证失败: {error_msg}")

        except MediationCase.DoesNotExist:
            logger.error(f"调解案件 {mediation_case_id} 不存在")
            return AjaxResult(code=404, msg="未找到对应的调解案件", state="fail").to_json_response()

        except Exception as e:
            logger.error(f"处理调解方案配置获取请求时发生异常: {str(e)}")
            return AjaxResult.fail(msg="服务器内部错误")

    def _process_confirmed_plan_config(self, mediation_case, mediation_case_id):
        """
        处理确认的调解方案配置快照

        当调解案件存在confirmed_plan_config字段时，直接使用该配置进行表达式计算，
        无需查询数据库中的调解方案记录。返回格式与原有逻辑保持一致。

        Args:
            mediation_case: 调解案件对象
            mediation_case_id: 调解案件ID

        Returns:
            AjaxResult: 包含处理后的配置数据的响应对象
        """
        try:
            confirmed_config = mediation_case.confirmed_plan_config

            # 检查确认配置是否为空
            if not confirmed_config:
                logger.warning(f"调解案件 {mediation_case_id} 的confirmed_plan_config为空")
                return AjaxResult.success(msg="获取成功", data=[])

            # 创建方案分组结构（模拟原有格式，使用确认的配置）
            plan_group = {
                "plan_id": "confirmed",  # 使用特殊标识表示这是确认的配置
                "plan_name": "已确认的调解方案",
                "plan_config": []
            }

            # 获取资产包和行号信息用于表达式计算
            asset_package = mediation_case.asset_package
            row_number = mediation_case.asset_package_row_number or 1

            # 处理确认的配置数据
            config_items = []
            if isinstance(confirmed_config, list):
                config_items = confirmed_config
            elif isinstance(confirmed_config, dict):
                config_items = [confirmed_config]
            else:
                logger.warning(f"调解案件 {mediation_case_id} 的confirmed_plan_config格式不正确: {type(confirmed_config)}")
                return AjaxResult.success(msg="获取成功", data=[])

            # 处理每个配置项
            for config_item in config_items:
                # 复制原始配置对象
                processed_item = config_item.copy() if isinstance(config_item, dict) else config_item

                # 检查配置对象是否包含必要字段
                if not isinstance(config_item, dict):
                    logger.warning(f"跳过非字典格式的配置项: {config_item}")
                    plan_group["plan_config"].append(processed_item)
                    continue

                logic_type = config_item.get("logic_type")
                expression = config_item.get("expression")

                # 如果没有logic_type或expression，直接添加到结果中
                if not logic_type or not expression:
                    logger.warning(f"配置项缺少logic_type或expression字段: {config_item}")
                    processed_item["value"] = None
                    plan_group["plan_config"].append(processed_item)
                    continue

                # 检查是否有关联的资产包用于表达式计算
                if not asset_package:
                    logger.warning(f"调解案件 {mediation_case_id} 没有关联资产包，无法进行表达式计算")
                    processed_item["value"] = None
                    plan_group["plan_config"].append(processed_item)
                    continue

                # 使用工具函数计算表达式
                calculation_result = calculate_expression_with_asset_data(
                    asset_package=asset_package,
                    row_number=row_number,
                    expression=expression,
                    logic_type=logic_type,
                )

                # 将计算结果添加到配置对象中
                if calculation_result["success"]:
                    processed_item["value"] = calculation_result["result"]
                    logger.info(
                        f"确认配置表达式计算成功，案件ID: {mediation_case_id}, 配置ID: {config_item.get('id', 'unknown')}, 结果: {calculation_result['result']}"
                    )
                else:
                    processed_item["value"] = None
                    logger.error(
                        f"确认配置表达式计算失败，案件ID: {mediation_case_id}, 配置ID: {config_item.get('id', 'unknown')}, 错误: {calculation_result['error']}"
                    )

                plan_group["plan_config"].append(processed_item)

            # 返回结果（保持数组格式以确保一致性）
            grouped_plan_configs = [plan_group]
            logger.info(
                f"确认方案配置处理完成，调解案件ID: {mediation_case_id}, 处理了 {len(plan_group['plan_config'])} 个配置项"
            )
            return AjaxResult.success(msg="获取成功", data=grouped_plan_configs)

        except Exception as e:
            logger.error(f"处理确认方案配置时发生异常: {str(e)}")
            return AjaxResult.fail(msg="处理确认方案配置失败")

    def _process_active_mediation_plans(self, mediation_case, mediation_case_id):
        """
        处理已生效的调解方案配置（原有逻辑）

        当调解案件的confirmed_plan_config字段为空时，查询相关的已生效调解方案，
        对每个方案的plan_config进行表达式计算处理。

        Args:
            mediation_case: 调解案件对象
            mediation_case_id: 调解案件ID

        Returns:
            AjaxResult: 包含处理后的配置数据的响应对象
        """
        try:
            # 查询相关的已生效调解方案
            # 查询条件：(asset_package=调解案件.asset_package 或 mediation_case=调解案件对象) 且 plan_status="active"
            query_conditions = Q(plan_status="active")

            if mediation_case.asset_package:
                query_conditions &= Q(asset_package=mediation_case.asset_package) | Q(mediation_case=mediation_case)
            else:
                query_conditions &= Q(mediation_case=mediation_case)

            mediation_plans = MediationPlan.objects.filter(query_conditions)

            # 检查是否找到相关的调解方案
            if not mediation_plans.exists():
                logger.warning(f"调解案件 {mediation_case_id} 没有找到相关的已生效调解方案")
                return AjaxResult.success(msg="获取成功", data=[])

            # 按调解方案分组处理配置信息
            grouped_plan_configs = []
            asset_package = mediation_case.asset_package

            # 获取调解案件在资产包中的行号，默认为1
            row_number = mediation_case.asset_package_row_number or 1

            # 遍历每个调解方案
            for plan in mediation_plans:
                # 创建方案分组结构
                plan_group = {"plan_id": plan.id, "plan_name": plan.plan_name, "plan_config": []}

                # 处理当前方案的plan_config配置
                if plan.plan_config:
                    # 获取配置项列表
                    config_items = []
                    if isinstance(plan.plan_config, list):
                        config_items = plan.plan_config
                    elif isinstance(plan.plan_config, dict):
                        config_items = [plan.plan_config]
                    else:
                        logger.warning(f"调解方案 {plan.id} 的plan_config格式不正确: {type(plan.plan_config)}")
                        continue

                    # 处理当前方案的每个配置项
                    for config_item in config_items:
                        # 复制原始配置对象
                        processed_item = config_item.copy() if isinstance(config_item, dict) else config_item

                        # 检查配置对象是否包含必要字段
                        if not isinstance(config_item, dict):
                            logger.warning(f"跳过非字典格式的配置项: {config_item}")
                            plan_group["plan_config"].append(processed_item)
                            continue

                        logic_type = config_item.get("logic_type")
                        expression = config_item.get("expression")

                        # 如果没有logic_type或expression，直接添加到结果中
                        if not logic_type or not expression:
                            logger.warning(f"配置项缺少logic_type或expression字段: {config_item}")
                            processed_item["value"] = None
                            plan_group["plan_config"].append(processed_item)
                            continue

                        # 检查是否有关联的资产包用于表达式计算
                        if not asset_package:
                            logger.warning(f"调解案件 {mediation_case_id} 没有关联资产包，无法进行表达式计算")
                            processed_item["value"] = None
                            plan_group["plan_config"].append(processed_item)
                            continue

                        # 使用工具函数计算表达式
                        calculation_result = calculate_expression_with_asset_data(
                            asset_package=asset_package,
                            row_number=row_number,
                            expression=expression,
                            logic_type=logic_type,
                        )

                        # 将计算结果添加到配置对象中
                        if calculation_result["success"]:
                            processed_item["value"] = calculation_result["result"]
                            logger.info(
                                f"表达式计算成功，方案ID: {plan.id}, 配置ID: {config_item.get('id', 'unknown')}, 结果: {calculation_result['result']}"
                            )
                        else:
                            processed_item["value"] = None
                            logger.error(
                                f"表达式计算失败，方案ID: {plan.id}, 配置ID: {config_item.get('id', 'unknown')}, 错误: {calculation_result['error']}"
                            )

                        plan_group["plan_config"].append(processed_item)

                # 将方案分组添加到结果中（即使配置为空也添加，保持数据完整性）
                grouped_plan_configs.append(plan_group)

            # 统计处理的配置项总数
            total_config_count = sum(len(group["plan_config"]) for group in grouped_plan_configs)
            logger.info(
                f"调解方案配置处理完成，调解案件ID: {mediation_case_id}, 处理了 {len(grouped_plan_configs)} 个方案，共 {total_config_count} 个配置项"
            )
            return AjaxResult.success(msg="获取成功", data=grouped_plan_configs)

        except Exception as e:
            logger.error(f"处理已生效调解方案配置时发生异常: {str(e)}")
            return AjaxResult.fail(msg="处理调解方案配置失败")


class MediationCaseListAPIView(GenericAPIView):
    """
    微信用户调解案件列表查询视图

    提供微信小程序端用户查询自己相关调解案件的功能。
    该视图通过WechatFaceAuthPermission权限验证用户身份和人脸核身状态，
    基于用户的真实姓名和身份证号查询相关的调解案件信息，支持查询所有案件或指定案件。
    """

    # 使用微信人脸核身权限认证
    permission_classes = [WechatFaceAuthPermission]

    serializer_class = MediationCaseQuerySerializer

    def get(self, request, *args, **kwargs):
        """
        获取微信用户相关的调解案件列表

        通过WechatFaceAuthPermission权限验证用户身份和人脸核身状态，
        基于已认证用户的真实姓名和身份证号查询相关的调解案件信息。
        支持查询所有案件或指定案件ID的单个案件。

        **请求参数：**

        **查询参数：**
        - mediation_case_id (整数, 可选): 调解案件ID，用于查询指定的调解案件

        **请求数据示例：**
        ```
        GET /mediation_management/mediation_case/wechat/list/
        GET /mediation_management/mediation_case/wechat/list/?mediation_case_id=123
        ```

        **响应数据结构：**
        成功响应：
        ```json
        {
            "code": 200,
            "msg": "查询成功",
            "state": "success",
            "data": [
                {
                    "id": 123,
                    "case_number": "GZTJ20250731ABC123",
                    "case_status": "in_progress",
                    "case_status_cn": "进行中",
                    "initiate_date": "2025-07-31"
                },
                {
                    "id": 124,
                    "case_number": "GZTJ20250731ABC124",
                    "case_status": "completed",
                    "case_status_cn": "已完成",
                    "initiate_date": "2025-07-30"
                }
            ]
        }
        ```

        错误响应：
        ```json
        {
            "code": 401,
            "msg": "用户未通过人脸核身认证",
            "state": "fail"
        }
        ```
        """
        try:
            # 获取查询参数
            mediation_case_id = request.query_params.get("mediation_case_id")

            # 使用序列化器验证参数
            serializer = self.get_serializer(data={"mediation_case_id": mediation_case_id})
            serializer.is_valid(raise_exception=True)

            # 获取验证后的参数
            validated_data = serializer.validated_data
            mediation_case_id = validated_data.get("mediation_case_id")

            # 通过权限验证后，直接从request.user获取用户信息
            # WechatFaceAuthPermission已确保用户通过认证且人脸核身成功
            try:
                system_user = SystemUser.objects.get(username=request.user.username)
            except SystemUser.DoesNotExist:
                logger.error(f"权限验证通过但未找到SystemUser记录: {request.user.username}")
                return AjaxResult.fail(msg="用户信息异常，请重新登录")

            # 获取用户的真实姓名和身份证号
            real_name = system_user.real_name
            id_card_number = system_user.id_card_number

            if not real_name or not id_card_number:
                logger.warning(
                    f"用户缺少必要的身份信息: {request.user.username}, real_name: {real_name}, id_card_number: {bool(id_card_number)}"
                )
                return AjaxResult.fail(msg="用户身份信息不完整")

            # 根据姓名和身份证号查询债务人信息
            try:
                debtor = DebtorBasicInfo.objects.get(debtor_name=real_name, id_number=id_card_number)
            except DebtorBasicInfo.DoesNotExist:
                logger.info(f"未找到对应的债务人信息: {real_name}, {id_card_number[:6]}****")
                return AjaxResult.success(msg="查询成功", data=[])

            # 查询相关的调解案件
            mediation_cases_query = MediationCase.objects.filter(debtor=debtor)

            # 如果提供了mediation_case_id，进行过滤
            if mediation_case_id is not None:
                mediation_cases_query = mediation_cases_query.filter(id=mediation_case_id)

            # 获取案件列表并按创建时间倒序排列
            mediation_cases = mediation_cases_query.order_by("-created_time")

            # 使用序列化器构建返回数据
            serializer = MediationCaseWechatSerializer(mediation_cases, many=True)
            case_list = serializer.data

            # 记录查询结果
            logger.info(f"微信用户调解案件查询成功: {request.user.username}, 找到 {len(case_list)} 个案件")

            return AjaxResult.success(msg="查询成功", data=case_list)

        except serializers.ValidationError as e:
            # 处理序列化器验证错误
            error_messages = []
            if hasattr(e, "detail"):
                for _, messages in e.detail.items():
                    if isinstance(messages, list):
                        error_messages.extend([str(msg) for msg in messages])
                    else:
                        error_messages.append(str(messages))
            error_msg = "; ".join(error_messages) if error_messages else str(e)
            logger.error(f"参数验证失败: {error_msg}")
            return AjaxResult.fail(msg=f"参数验证失败: {error_msg}")

        except Exception as e:
            logger.error(f"处理微信用户调解案件查询请求时发生异常: {str(e)}")
            return AjaxResult.fail(msg="服务器内部错误")


class MediationAgreementPDFSerializer(serializers.Serializer):
    """
    调解协议PDF生成接口的参数验证序列化器，用于规范化请求参数的格式和约束。
    该序列化器主要用于API文档自动生成和开发调试时的参数验证。
    """

    case_number = serializers.CharField(
        max_length=50,
        help_text="调解案件号，格式：GZTJ+YYYYMMDD+6位安全哈希，用于双重校验"
    )


class MediationAgreementPDFView(GenericAPIView):
    """
    调解协议PDF生成和下载视图

    提供调解协议PDF文件的生成和安全下载功能。该视图通过调解案件ID和调解案件号进行双重校验，
    确保数据安全性，生成包含调解信息、调解方案和电子签名的标准化PDF协议文件。
    生成的PDF文件将存储到调解案件的mediation_agreement字段中，并提供安全的下载链接。
    """

    # 配置序列化器类用于API文档生成
    serializer_class = MediationAgreementPDFSerializer

    # 配置权限类：需要用户认证
    # permission_classes = [MyPermission]  # 可根据需要调整权限

    def get(self, request, case_id, *args, **kwargs):
        """
        生成调解协议PDF文件并提供下载链接

        根据调解案件ID和调解案件号进行双重校验，生成包含调解信息、调解方案和电子签名的
        标准化PDF协议文件。生成的PDF文件将存储到调解案件对象中，并返回安全的下载链接。

        **请求参数：**

        **路径参数：**
        - case_id (整数, 必需): 调解案件ID，通过URL路径参数传递

        **查询参数：**
        - case_number (字符串, 必需): 调解案件号，用于双重校验，格式：GZTJ+YYYYMMDD+6位安全哈希

        **请求数据示例：**
        ```
        GET /mediation_management/mediation_case/123/agreement_pdf/?case_number=GZTJ20250801ABC123
        ```

        **响应数据结构：**

        成功响应：
        ```json
        {
            "code": 200,
            "msg": "PDF生成成功",
            "state": "success",
            "data": {
                "download_url": "/user/files/download/550e8400-e29b-41d4-a716-446655440000/",
                "file_name": "调解协议_GZTJ20250801ABC123.pdf",
                "file_size": 1024000
            }
        }
        ```

        错误响应：
        ```json
        {
            "code": 404,
            "msg": "调解案件不存在或案件号不匹配",
            "state": "fail"
        }
        ```
        ```json
        {
            "code": 400,
            "msg": "调解案件缺少必要的配置信息",
            "state": "fail"
        }
        ```
        """
        try:
            # 获取查询参数
            case_number = request.GET.get('case_number')
            if not case_number:
                logger.warning(f"缺少调解案件号参数，案件ID: {case_id}")
                return AjaxResult.fail(msg="缺少调解案件号参数")

            # 验证参数格式
            serializer_data = {"case_number": case_number}
            serializer = self.get_serializer(data=serializer_data)
            serializer.is_valid(raise_exception=True)

            # 记录请求日志
            logger.info(f"开始生成调解协议PDF，案件ID: {case_id}, 案件号: {case_number}")

            # 双重校验：通过案件ID和案件号查询调解案件
            try:
                mediation_case = MediationCase.objects.get(id=case_id, case_number=case_number)
            except MediationCase.DoesNotExist:
                logger.warning(f"调解案件不存在或案件号不匹配，案件ID: {case_id}, 案件号: {case_number}")
                return AjaxResult.not_found(msg="调解案件不存在或案件号不匹配")

            # 验证必要的数据是否存在
            if not mediation_case.confirmed_mediation_config and not mediation_case.confirmed_plan_config:
                logger.warning(f"调解案件 {case_id} 缺少确认的配置信息")
                return AjaxResult.fail(msg="调解案件缺少必要的配置信息")

            # 生成PDF文件
            pdf_content = self._generate_pdf_content(mediation_case)
            if not pdf_content:
                logger.error(f"PDF内容生成失败，案件ID: {case_id}")
                return AjaxResult.fail(msg="PDF生成失败")

            # 创建MediationCaseFile实例来存储PDF文件
            file_name = f"调解协议_{case_number}.pdf"

            # 创建MediationCaseFile对象
            case_file = MediationCaseFile(file_name=file_name)
            case_file.file.save(file_name, ContentFile(pdf_content), save=False)
            case_file.save()

            # 将文件关联到调解案件的附件中
            mediation_case.attachments.add(case_file)

            # 设置签署日期为当前时间（如果尚未设置）
            if not mediation_case.signature_date:
                mediation_case.signature_date = datetime.now()
                mediation_case.save()

            # 使用FileSecurityHelper生成安全下载链接
            download_url = FileSecurityHelper.generate_secure_download_url(case_file)
            if not download_url:
                logger.warning(f"无法生成安全下载链接，案件ID: {case_id}")
                # 如果无法生成安全链接，返回错误
                return AjaxResult.fail(msg="无法生成安全下载链接")

            # 构建响应数据
            response_data = {
                "download_url": download_url,
                "file_name": file_name,
                "file_size": len(pdf_content) if pdf_content else 0,
                "secure_token": str(case_file.secure_token) if case_file.secure_token else None
            }

            logger.info(f"调解协议PDF生成成功，案件ID: {case_id}, 文件大小: {len(pdf_content)} bytes")
            return AjaxResult.success(msg="PDF生成成功", data=response_data)

        except serializers.ValidationError as e:
            # 参数验证失败
            error_messages = []
            if hasattr(e, "detail"):
                for _, messages in e.detail.items():
                    if isinstance(messages, list):
                        error_messages.extend([str(msg) for msg in messages])
                    else:
                        error_messages.append(str(messages))
            error_msg = "; ".join(error_messages) if error_messages else str(e)
            logger.error(f"参数验证失败: {error_msg}")
            return AjaxResult.fail(msg=f"参数验证失败: {error_msg}")

        except Exception as e:
            logger.error(f"生成调解协议PDF时发生异常: {str(e)}")
            return AjaxResult.fail(msg="PDF生成失败")

    def _generate_pdf_content(self, mediation_case):
        """
        生成调解协议PDF内容

        根据调解案件对象生成标准化的调解协议PDF文件内容，包含标题、案件信息、
        调解信息、调解方案和电子签名等完整内容。支持中文字体显示。

        Args:
            mediation_case: 调解案件对象

        Returns:
            bytes: PDF文件的二进制内容，如果生成失败则返回None
        """
        try:
            # 注册中文字体
            chinese_font_name = self._register_chinese_font()

            # 创建内存中的PDF文档
            from io import BytesIO
            buffer = BytesIO()

            # 创建PDF文档对象
            doc = SimpleDocTemplate(
                buffer,
                pagesize=A4,
                rightMargin=72,
                leftMargin=72,
                topMargin=72,
                bottomMargin=72
            )

            # 获取样式表
            styles = getSampleStyleSheet()

            # 创建支持中文的自定义样式
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Heading1'],
                fontSize=18,
                spaceAfter=30,
                alignment=TA_CENTER,
                fontName=chinese_font_name
            )

            heading_style = ParagraphStyle(
                'CustomHeading',
                parent=styles['Heading2'],
                fontSize=14,
                spaceAfter=12,
                spaceBefore=20,
                fontName=chinese_font_name
            )

            normal_style = ParagraphStyle(
                'CustomNormal',
                parent=styles['Normal'],
                fontSize=12,
                spaceAfter=6,
                fontName=chinese_font_name
            )

            # 构建PDF内容
            story = []

            # 1. 标题
            title = Paragraph("调解协议", title_style)
            story.append(title)
            story.append(Spacer(1, 20))

            # 2. 左上角信息
            case_info = [
                f"调解案件号：{mediation_case.case_number}",
                f"发起日期：{mediation_case.initiate_date.strftime('%Y-%m-%d') if mediation_case.initiate_date else '未设置'}",
                f"债权人：{mediation_case.creditor.creditor_name if mediation_case.creditor else '未设置'}",
                f"债务人：{mediation_case.debtor.debtor_name if mediation_case.debtor else '未设置'}"
            ]

            for info in case_info:
                story.append(Paragraph(info, normal_style))
            story.append(Spacer(1, 20))

            # 3. 调解信息部分
            if mediation_case.confirmed_mediation_config:
                story.append(Paragraph("调解信息", heading_style))
                mediation_items = self._process_config_data(
                    mediation_case,
                    mediation_case.confirmed_mediation_config
                )
                for item in mediation_items:
                    content = f"{item.get('title', '')}：{item.get('value', '')}"
                    story.append(Paragraph(content, normal_style))
                story.append(Spacer(1, 15))

            # 4. 调解方案部分
            if mediation_case.confirmed_plan_config:
                story.append(Paragraph("调解方案", heading_style))
                plan_items = self._process_config_data(
                    mediation_case,
                    mediation_case.confirmed_plan_config
                )
                for item in plan_items:
                    content = f"{item.get('title', '')}：{item.get('value', '')}"
                    story.append(Paragraph(content, normal_style))
                story.append(Spacer(1, 30))

            # 5. 右下角签名信息
            # 构建签署人信息（第一行）
            signer_info = []

            # 添加电子签名图片（如果存在）
            if mediation_case.electronic_signature:
                try:
                    # 检查电子签名文件是否存在
                    if os.path.exists(mediation_case.electronic_signature.path):
                        signature_img = Image(
                            mediation_case.electronic_signature.path,
                            width=2*inch,
                            height=1*inch
                        )
                        signer_info.append(signature_img)
                except Exception as e:
                    logger.warning(f"无法加载电子签名图片: {str(e)}")
                    signer_info.append(Paragraph("签署人：[电子签名]", normal_style))
            else:
                signer_info.append(Paragraph("签署人：[未签署]", normal_style))

            # 构建签署日期信息（第二行）
            date_info = []
            if mediation_case.signature_date:
                # 如果签署日期不为NULL，显示格式化日期
                sign_date = mediation_case.signature_date.strftime('%Y年%m月%d日')
                date_info.append(Paragraph(f"签署日期：{sign_date}", normal_style))
            else:
                # 如果签署日期为NULL，显示未签署
                date_info.append(Paragraph("签署日期：[未签署]", normal_style))

            # 添加空白间距，将签名信息推到页面底部
            story.append(Spacer(1, 50))

            # 创建两行签名表格（右对齐，紧贴右下角）
            signature_data = [
                signer_info,    # 第一行：签署人信息
                date_info       # 第二行：签署日期信息
            ]

            # 计算表格宽度，确保右对齐效果
            page_width = A4[0] - 144  # 页面宽度减去左右边距（72*2）
            table_width = 3*inch      # 签名表格宽度

            signature_table = Table(signature_data, colWidths=[table_width])
            signature_table.setStyle(TableStyle([
                ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),      # 内容右对齐
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),    # 垂直居中对齐
                ('RIGHTPADDING', (0, 0), (-1, -1), 0),     # 右边距为0
                ('LEFTPADDING', (0, 0), (-1, -1), 0),      # 左边距为0
                ('BOTTOMPADDING', (0, 0), (-1, -1), 4),    # 行间距
                ('TOPPADDING', (0, 0), (-1, -1), 4),       # 行间距
                ('LINEBELOW', (0, 0), (-1, 0), 0, 'white'), # 第一行下方无边框
            ]))

            # 创建一个包装表格来实现右对齐定位
            wrapper_data = [['', signature_table]]
            wrapper_table = Table(wrapper_data, colWidths=[page_width - table_width, table_width])
            wrapper_table.setStyle(TableStyle([
                ('ALIGN', (1, 0), (1, 0), 'RIGHT'),        # 签名表格右对齐
                ('VALIGN', (0, 0), (-1, -1), 'BOTTOM'),    # 底部对齐
                ('LEFTPADDING', (0, 0), (-1, -1), 0),      # 无边距
                ('RIGHTPADDING', (0, 0), (-1, -1), 0),     # 无边距
                ('TOPPADDING', (0, 0), (-1, -1), 0),       # 无边距
                ('BOTTOMPADDING', (0, 0), (-1, -1), 0),    # 无边距
            ]))

            story.append(wrapper_table)

            # 生成PDF
            doc.build(story)

            # 获取PDF内容
            pdf_content = buffer.getvalue()
            buffer.close()

            logger.info(f"PDF内容生成成功，大小: {len(pdf_content)} bytes")
            return pdf_content

        except Exception as e:
            logger.error(f"生成PDF内容时发生异常: {str(e)}")
            return None

    def _process_config_data(self, mediation_case, config_data):
        """
        处理配置数据，计算表达式并返回格式化的结果

        对调解配置或方案配置中的表达式进行计算处理，返回包含标题和计算结果的列表。

        Args:
            mediation_case: 调解案件对象
            config_data: 配置数据（JSON格式的列表）

        Returns:
            list: 包含处理后数据的列表，每个元素包含title和value字段
        """
        try:
            processed_items = []

            if not config_data or not isinstance(config_data, list):
                logger.warning("配置数据为空或格式不正确")
                return processed_items

            for item in config_data:
                if not isinstance(item, dict):
                    continue

                title = item.get('title', '')
                expression = item.get('expression', '')
                logic_type = item.get('logic_type', 'text_formatting')

                # 如果没有表达式，直接使用原始值
                if not expression:
                    value = item.get('value', '')
                else:
                    # 使用表达式计算器处理表达式
                    if mediation_case.asset_package and mediation_case.asset_package_row_number:
                        calc_result = calculate_expression_with_asset_data(
                            mediation_case.asset_package,
                            mediation_case.asset_package_row_number,
                            expression,
                            logic_type
                        )
                        if calc_result.get('success'):
                            value = calc_result.get('result', '')
                        else:
                            logger.warning(f"表达式计算失败: {calc_result.get('error', '')}")
                            value = item.get('value', expression)  # 使用原始值作为备用
                    else:
                        logger.warning("缺少资产包或行号信息，无法计算表达式")
                        value = item.get('value', expression)  # 使用原始值作为备用

                processed_items.append({
                    'title': title,
                    'value': str(value) if value is not None else ''
                })

            return processed_items

        except Exception as e:
            logger.error(f"处理配置数据时发生异常: {str(e)}")
            return []

    def _register_chinese_font(self):
        """
        注册中文字体以支持PDF中的中文显示

        尝试注册系统中可用的中文字体，提供跨平台兼容性。
        优先级：微软雅黑 > 黑体 > 宋体 > 默认字体

        Returns:
            str: 注册成功的字体名称
        """
        try:
            # 定义中文字体候选列表（按优先级排序）
            font_candidates = [
                # Windows 系统字体
                {
                    'name': 'Microsoft-YaHei',
                    'paths': [
                        'C:/Windows/Fonts/msyh.ttc',  # 微软雅黑
                        'C:/Windows/Fonts/msyh.ttf',
                        '/System/Library/Fonts/PingFang.ttc',  # macOS 苹方字体
                        '/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf',  # Linux 备选
                    ]
                },
                {
                    'name': 'SimHei',
                    'paths': [
                        'C:/Windows/Fonts/simhei.ttf',  # 黑体
                        '/System/Library/Fonts/STHeiti Light.ttc',  # macOS 黑体
                        '/usr/share/fonts/truetype/wqy/wqy-zenhei.ttc',  # Linux 文泉驿正黑
                    ]
                },
                {
                    'name': 'SimSun',
                    'paths': [
                        'C:/Windows/Fonts/simsun.ttc',  # 宋体
                        'C:/Windows/Fonts/simsun.ttf',
                        '/System/Library/Fonts/Songti.ttc',  # macOS 宋体
                        '/usr/share/fonts/truetype/arphic/uming.ttc',  # Linux AR PL UMing
                    ]
                }
            ]

            # 尝试注册字体
            for font_info in font_candidates:
                font_name = font_info['name']

                # 检查字体是否已经注册
                try:
                    # 尝试使用字体，如果已注册则直接返回
                    pdfmetrics.getFont(font_name)
                    logger.info(f"字体 {font_name} 已注册，直接使用")
                    return font_name
                except:
                    # 字体未注册，继续尝试注册
                    pass

                # 尝试从候选路径中找到字体文件并注册
                for font_path in font_info['paths']:
                    if os.path.exists(font_path):
                        try:
                            # 注册字体
                            pdfmetrics.registerFont(TTFont(font_name, font_path))
                            logger.info(f"成功注册中文字体: {font_name} from {font_path}")
                            return font_name
                        except Exception as e:
                            logger.warning(f"注册字体 {font_name} 失败: {str(e)}")
                            continue

            # 如果所有中文字体都注册失败，尝试注册 ReportLab 内置的中文字体
            try:
                from reportlab.pdfbase.cidfonts import UnicodeCIDFont
                pdfmetrics.registerFont(UnicodeCIDFont('STSong-Light'))
                logger.info("使用 ReportLab 内置中文字体: STSong-Light")
                return 'STSong-Light'
            except Exception as e:
                logger.warning(f"注册内置中文字体失败: {str(e)}")

            # 最后的备选方案：使用 Helvetica 字体（不支持中文，但不会报错）
            logger.warning("所有中文字体注册失败，使用 Helvetica 字体（可能无法正确显示中文）")
            return 'Helvetica'

        except Exception as e:
            logger.error(f"注册中文字体时发生异常: {str(e)}")
            return 'Helvetica'
